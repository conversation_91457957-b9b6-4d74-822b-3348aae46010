import 'package:flutter/material.dart';

class CertificateCard extends StatelessWidget {
  final String title;
  final String issueDate;

  const CertificateCard({
    super.key,
    required this.title,
    required this.issueDate,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: const Color(0xFFFFFFFF),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      elevation: 4.0,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left Section (Thumbnail)
            Container(
              width: 100,
              height: 75,
              decoration: BoxDecoration(
                color: const Color(0xFFEEEEEE), // Soft gray
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: const Icon(
                Icons.article_outlined,
                color: Colors.grey,
                size: 40,
              ),
            ),
            const SizedBox(width: 16.0),
            // Middle Section (Details)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.0,
                      color: Color(0xFF212121), // Primary text color
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4.0),
                  Text(
                    'Issued: $issueDate',
                    style: const TextStyle(
                      fontSize: 14.0,
                      color: Color(0xFF757575), // Secondary text color
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16.0),
            // Right Section (Action Icon)
            const Icon(
              Icons.download_outlined,
              color: Color(0xFF00838F), // Accent color
              size: 28,
            ),
          ],
        ),
      ),
    );
  }
}
