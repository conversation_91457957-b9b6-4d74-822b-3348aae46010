import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_id.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('id'),
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Self Eng'**
  String get appTitle;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @library.
  ///
  /// In en, this message translates to:
  /// **'Library'**
  String get library;

  /// No description provided for @games.
  ///
  /// In en, this message translates to:
  /// **'Games'**
  String get games;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @counterPage.
  ///
  /// In en, this message translates to:
  /// **'Counter page'**
  String get counterPage;

  /// No description provided for @buttonPushedTimes.
  ///
  /// In en, this message translates to:
  /// **'You have pushed the button this many times:'**
  String get buttonPushedTimes;

  /// No description provided for @increment.
  ///
  /// In en, this message translates to:
  /// **'Increment'**
  String get increment;

  /// No description provided for @somethingWentWrong.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get somethingWentWrong;

  /// No description provided for @imageNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'Image not available'**
  String get imageNotAvailable;

  /// No description provided for @onboarding1.
  ///
  /// In en, this message translates to:
  /// **'Start Your English\nAdventure! 🚀'**
  String get onboarding1;

  /// No description provided for @onboarding2.
  ///
  /// In en, this message translates to:
  /// **'Learn English,\nLimitless!🤳'**
  String get onboarding2;

  /// No description provided for @onboarding3.
  ///
  /// In en, this message translates to:
  /// **'Get Started Now! ⏩'**
  String get onboarding3;

  /// No description provided for @onboardingSingup.
  ///
  /// In en, this message translates to:
  /// **'Register with'**
  String get onboardingSingup;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select a language'**
  String get selectLanguage;

  /// No description provided for @selectLanguageDes.
  ///
  /// In en, this message translates to:
  /// **'Please select the language you want to use'**
  String get selectLanguageDes;

  /// No description provided for @languageIndo.
  ///
  /// In en, this message translates to:
  /// **'Bahasa Indonesia'**
  String get languageIndo;

  /// No description provided for @languageEngl.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get languageEngl;

  /// No description provided for @choose.
  ///
  /// In en, this message translates to:
  /// **'Choose'**
  String get choose;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @later.
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get later;

  /// No description provided for @shortDescription.
  ///
  /// In en, this message translates to:
  /// **'Describe briefly and clearly'**
  String get shortDescription;

  /// No description provided for @questionnaireOtherQ.
  ///
  /// In en, this message translates to:
  /// **'Mention your other main goals:'**
  String get questionnaireOtherQ;

  /// No description provided for @questionnaireOtherA.
  ///
  /// In en, this message translates to:
  /// **'Type briefly'**
  String get questionnaireOtherA;

  /// No description provided for @questionnaireFinish.
  ///
  /// In en, this message translates to:
  /// **'Thank you for completing the self-assessment questionnaire!'**
  String get questionnaireFinish;

  /// No description provided for @questionnaireFinishDesc.
  ///
  /// In en, this message translates to:
  /// **'Your responses will help tailor the learning plan to meet your specific needs, interests, and goals. Let\'s begin this English learning journey together! 🚀📚✨🧑‍🤝‍🧑🌏'**
  String get questionnaireFinishDesc;

  /// No description provided for @questionnaireIWilling.
  ///
  /// In en, this message translates to:
  /// **'Yes, let\'s begin'**
  String get questionnaireIWilling;

  /// No description provided for @please_type.
  ///
  /// In en, this message translates to:
  /// **'Please type here'**
  String get please_type;

  /// No description provided for @please_type_number.
  ///
  /// In en, this message translates to:
  /// **'Please type number'**
  String get please_type_number;

  /// No description provided for @doIt.
  ///
  /// In en, this message translates to:
  /// **'Do it'**
  String get doIt;

  /// No description provided for @instruction.
  ///
  /// In en, this message translates to:
  /// **'Instructions'**
  String get instruction;

  /// No description provided for @testInstruction.
  ///
  /// In en, this message translates to:
  /// **'Test Instructions'**
  String get testInstruction;

  /// No description provided for @testInstructionDesc.
  ///
  /// In en, this message translates to:
  /// **'Take a deep breath, focus, and read the instructions carefully before answering the questions'**
  String get testInstructionDesc;

  /// No description provided for @testInstructionRule.
  ///
  /// In en, this message translates to:
  /// **'1. You have 15 minutes to complete the test.\n2. Read each question carefully and select the most appropriate answer.\n3. Choose the option that best reflects your knowledge or understanding.\n4. There is no penalty for guessing, so try to answer all questions within the given time.\n5. Once the time is up, the test will automatically end, and your responses will be evaluated.\n6. Please do not refer to any external sources or consult with others during the test.\n7. At the end of the test, you will receive feedback on your performance and recommendations for your proficiency level in the integrated self-learning speaking program.'**
  String get testInstructionRule;

  /// No description provided for @diagnosticTests.
  ///
  /// In en, this message translates to:
  /// **'Diagnostic Tests'**
  String get diagnosticTests;

  /// No description provided for @areYouReady.
  ///
  /// In en, this message translates to:
  /// **'Are you ready?'**
  String get areYouReady;

  /// No description provided for @yesIAmReady.
  ///
  /// In en, this message translates to:
  /// **'Yes, I am ready.'**
  String get yesIAmReady;

  /// No description provided for @sorry_not_ready_yet.
  ///
  /// In en, this message translates to:
  /// **'Sorry, not ready yet.'**
  String get sorry_not_ready_yet;

  /// No description provided for @areYouSure.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get areYouSure;

  /// No description provided for @yesIAmSure.
  ///
  /// In en, this message translates to:
  /// **'Yes, I am sure'**
  String get yesIAmSure;

  /// No description provided for @notYetLater.
  ///
  /// In en, this message translates to:
  /// **'Not yet, later'**
  String get notYetLater;

  /// No description provided for @continue1.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continue1;

  /// No description provided for @timesUpStudyGroup1.
  ///
  /// In en, this message translates to:
  /// **'Oh no, the test time is up 😔. Click the button '**
  String get timesUpStudyGroup1;

  /// No description provided for @timesUpStudyGroup2.
  ///
  /// In en, this message translates to:
  /// **'‘continue’'**
  String get timesUpStudyGroup2;

  /// No description provided for @timesUpStudyGroup3.
  ///
  /// In en, this message translates to:
  /// **' to find out your study group'**
  String get timesUpStudyGroup3;

  /// No description provided for @finallyResult.
  ///
  /// In en, this message translates to:
  /// **'Finally, your test results are out 🎉'**
  String get finallyResult;

  /// No description provided for @processingTime.
  ///
  /// In en, this message translates to:
  /// **'Processing time'**
  String get processingTime;

  /// No description provided for @totalScore.
  ///
  /// In en, this message translates to:
  /// **'Total Score'**
  String get totalScore;

  /// No description provided for @learningLevel.
  ///
  /// In en, this message translates to:
  /// **'Learning Level'**
  String get learningLevel;

  /// No description provided for @finallyResultDesc.
  ///
  /// In en, this message translates to:
  /// **'Happy learning, take your understanding to the next level, and enjoy every moment of this journey!'**
  String get finallyResultDesc;

  /// No description provided for @questionnaireOnboarding1.
  ///
  /// In en, this message translates to:
  /// **'Welcome to the Diagnostic Test for the Integrated Self-Learning Speaking Program!'**
  String get questionnaireOnboarding1;

  /// No description provided for @questionnaireOnboarding2.
  ///
  /// In en, this message translates to:
  /// **'This test aims to assess your English speaking proficiency at various stages'**
  String get questionnaireOnboarding2;

  /// No description provided for @questionnaireOnboarding3.
  ///
  /// In en, this message translates to:
  /// **'Your performance in this test will help determine the level of the program that best suits your needs'**
  String get questionnaireOnboarding3;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @point.
  ///
  /// In en, this message translates to:
  /// **'Point'**
  String get point;

  /// No description provided for @level.
  ///
  /// In en, this message translates to:
  /// **'Level'**
  String get level;

  /// No description provided for @information.
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get information;

  /// No description provided for @questionnaireCongrat1.
  ///
  /// In en, this message translates to:
  /// **'Discover the world through the wonders of English!'**
  String get questionnaireCongrat1;

  /// No description provided for @questionnaireCongrat1Desc.
  ///
  /// In en, this message translates to:
  /// **'Whether you\'re just starting your journey or aiming to reach a new level of fluency, now is the perfect time to begin.'**
  String get questionnaireCongrat1Desc;

  /// No description provided for @questionnaireCongrat2.
  ///
  /// In en, this message translates to:
  /// **'Let\'s embark on this transformative adventure together,'**
  String get questionnaireCongrat2;

  /// No description provided for @questionnaireCongrat2Desc.
  ///
  /// In en, this message translates to:
  /// **'An adventure where every word learned is one step closer to exciting opportunities and limitless experiences'**
  String get questionnaireCongrat2Desc;

  /// No description provided for @questionnaireCongrat3.
  ///
  /// In en, this message translates to:
  /// **'Ignite your passion for English today.'**
  String get questionnaireCongrat3;

  /// No description provided for @questionnaireCongrat3Desc.
  ///
  /// In en, this message translates to:
  /// **'Are you ready to take this journey? Let\'s dive in and make your English learning dreams come true! 📚🚀'**
  String get questionnaireCongrat3Desc;

  /// No description provided for @pronunciationChallenge.
  ///
  /// In en, this message translates to:
  /// **'Pronunciation Challenge'**
  String get pronunciationChallenge;

  /// No description provided for @conversationVideo.
  ///
  /// In en, this message translates to:
  /// **'Conversation Video'**
  String get conversationVideo;

  /// No description provided for @listeningMastery.
  ///
  /// In en, this message translates to:
  /// **'Listening Mastery'**
  String get listeningMastery;

  /// No description provided for @speakingArena.
  ///
  /// In en, this message translates to:
  /// **'Speaking Arena'**
  String get speakingArena;

  /// No description provided for @record.
  ///
  /// In en, this message translates to:
  /// **'Record'**
  String get record;

  /// No description provided for @stage1Speaking.
  ///
  /// In en, this message translates to:
  /// **'Listen and follow the audio and scripts'**
  String get stage1Speaking;

  /// No description provided for @stage1SpeakingDesc.
  ///
  /// In en, this message translates to:
  /// **'Listen to the audio and read the script carefully.'**
  String get stage1SpeakingDesc;

  /// No description provided for @stage2Speaking.
  ///
  /// In en, this message translates to:
  /// **'You act\nas the Questioner'**
  String get stage2Speaking;

  /// No description provided for @stage3Speaking.
  ///
  /// In en, this message translates to:
  /// **'You act\nas the Responder'**
  String get stage3Speaking;

  /// No description provided for @nextSection.
  ///
  /// In en, this message translates to:
  /// **'Ready for the next challenge?'**
  String get nextSection;

  /// Welcome the user message
  ///
  /// In en, this message translates to:
  /// **'Hi, {userName} 👋'**
  String hiUser(String userName);

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome!'**
  String get welcome;

  /// No description provided for @welcome_back.
  ///
  /// In en, this message translates to:
  /// **'Welcome back! What\'s up?🥳'**
  String get welcome_back;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @seeAll.
  ///
  /// In en, this message translates to:
  /// **'See all'**
  String get seeAll;

  /// No description provided for @selectedLanguageDesc1.
  ///
  /// In en, this message translates to:
  /// **'You have chosen'**
  String get selectedLanguageDesc1;

  /// No description provided for @express_yourself_in_english.
  ///
  /// In en, this message translates to:
  /// **'Ready to chat in English? Let\'s go! 💬'**
  String get express_yourself_in_english;

  /// No description provided for @join_the_community.
  ///
  /// In en, this message translates to:
  /// **'Join community'**
  String get join_the_community;

  /// No description provided for @instructions.
  ///
  /// In en, this message translates to:
  /// **'Instructions'**
  String get instructions;

  /// No description provided for @listen_and_imitate.
  ///
  /// In en, this message translates to:
  /// **'Listen and Imitate'**
  String get listen_and_imitate;

  /// No description provided for @listen_and_imitate_desc.
  ///
  /// In en, this message translates to:
  /// **'Listen to the pronunciation recording, focus on the sounds and stress, then imitate it out loud.'**
  String get listen_and_imitate_desc;

  /// No description provided for @record_and_analyze.
  ///
  /// In en, this message translates to:
  /// **'Record and Improve'**
  String get record_and_analyze;

  /// No description provided for @record_and_analyze_desc.
  ///
  /// In en, this message translates to:
  /// **'Record your pronunciation, get AI analysis, compare it with the model, and adjust until perfect.'**
  String get record_and_analyze_desc;

  /// No description provided for @compare_and_adjust.
  ///
  /// In en, this message translates to:
  /// **'Compare and Adjust'**
  String get compare_and_adjust;

  /// No description provided for @compare_and_adjust_desc.
  ///
  /// In en, this message translates to:
  /// **'Compare your pronunciation with the recorded model and make necessary adjustments based on the feedback.'**
  String get compare_and_adjust_desc;

  /// No description provided for @practice_and_perfect.
  ///
  /// In en, this message translates to:
  /// **'Practice and Perfect'**
  String get practice_and_perfect;

  /// No description provided for @practice_and_perfect_desc.
  ///
  /// In en, this message translates to:
  /// **'Keep practicing and refining your pronunciation until you feel confident in accurately pronouncing each word and expression.'**
  String get practice_and_perfect_desc;

  /// No description provided for @watch_and_analyze.
  ///
  /// In en, this message translates to:
  /// **'Watch and Analyze'**
  String get watch_and_analyze;

  /// No description provided for @watch_and_analyze_desc1.
  ///
  /// In en, this message translates to:
  /// **'Watch the video with subtitles on, repeat important parts, and focus on vocabulary, grammar, and conversation themes.'**
  String get watch_and_analyze_desc1;

  /// No description provided for @watch_and_analyze_desc2.
  ///
  /// In en, this message translates to:
  /// **'Rewatch segments, pause to focus on specific parts, and break the conversation into smaller sections.'**
  String get watch_and_analyze_desc2;

  /// No description provided for @focus_on_vocabulary_and_grammar.
  ///
  /// In en, this message translates to:
  /// **'Focus on vocabulary and grammar'**
  String get focus_on_vocabulary_and_grammar;

  /// No description provided for @focus_on_vocabulary_and_grammar_desc1.
  ///
  /// In en, this message translates to:
  /// **'Use captions to reinforce understanding, vocabulary, and grammatical structures.'**
  String get focus_on_vocabulary_and_grammar_desc1;

  /// No description provided for @focus_on_vocabulary_and_grammar_desc2.
  ///
  /// In en, this message translates to:
  /// **'Note unfamiliar words or phrases and their meanings.'**
  String get focus_on_vocabulary_and_grammar_desc2;

  /// No description provided for @pronunciation_practice.
  ///
  /// In en, this message translates to:
  /// **'Pronunciation Practice'**
  String get pronunciation_practice;

  /// No description provided for @pronunciation_practice_desc1.
  ///
  /// In en, this message translates to:
  /// **'Listen and mimic the pronunciation and intonation of the speaker.'**
  String get pronunciation_practice_desc1;

  /// No description provided for @pronunciation_practice_desc2.
  ///
  /// In en, this message translates to:
  /// **'Pay attention to stress patterns, rhythm, and word connections, using the text as a visual aid.'**
  String get pronunciation_practice_desc2;

  /// No description provided for @reflect_and_review.
  ///
  /// In en, this message translates to:
  /// **'Practice and Review'**
  String get reflect_and_review;

  /// No description provided for @reflect_and_review_desc1.
  ///
  /// In en, this message translates to:
  /// **'Imitate the pronunciation, pay attention to the intonation patterns, and review the notes to improve areas that need enhancement.'**
  String get reflect_and_review_desc1;

  /// No description provided for @reflect_and_review_desc2.
  ///
  /// In en, this message translates to:
  /// **'Review your notes and identify areas that need improvement, considering your language learning goals and interests.'**
  String get reflect_and_review_desc2;

  /// No description provided for @listen_actively.
  ///
  /// In en, this message translates to:
  /// **'Listen and Take Notes'**
  String get listen_actively;

  /// No description provided for @listen_actively_desc.
  ///
  /// In en, this message translates to:
  /// **'Use headphones/speakers, listen actively, and take notes to understand the recording.'**
  String get listen_actively_desc;

  /// No description provided for @repeat_and_review.
  ///
  /// In en, this message translates to:
  /// **'Repeat and Review'**
  String get repeat_and_review;

  /// No description provided for @repeat_and_review_desc.
  ///
  /// In en, this message translates to:
  /// **'Listen to the recording several times, pause and replay as needed.'**
  String get repeat_and_review_desc;

  /// No description provided for @answer_the_questions.
  ///
  /// In en, this message translates to:
  /// **'Answer the Questions'**
  String get answer_the_questions;

  /// No description provided for @answer_the_questions_desc.
  ///
  /// In en, this message translates to:
  /// **'Read the questions carefully and answer them based on the information provided in the conversation.'**
  String get answer_the_questions_desc;

  /// No description provided for @submit_and_review.
  ///
  /// In en, this message translates to:
  /// **'Answer and Review'**
  String get submit_and_review;

  /// No description provided for @submit_and_review_desc.
  ///
  /// In en, this message translates to:
  /// **'Choose the available answers, submit for evaluation, and review the feedback to improve your listening skills.'**
  String get submit_and_review_desc;

  /// No description provided for @listen_and_follow.
  ///
  /// In en, this message translates to:
  /// **'Listen, Follow, and Record'**
  String get listen_and_follow;

  /// No description provided for @listen_and_follow_desc.
  ///
  /// In en, this message translates to:
  /// **'Play the audio recording, follow the script to mimic the pronunciation and intonation, then record your voice.'**
  String get listen_and_follow_desc;

  /// No description provided for @repeat_the_practice.
  ///
  /// In en, this message translates to:
  /// **'Compare and Improve'**
  String get repeat_the_practice;

  /// No description provided for @repeat_the_practice_desc.
  ///
  /// In en, this message translates to:
  /// **'Compare your recording with the model audio, review the strengths and weaknesses, and keep practicing to improve your speaking skills.'**
  String get repeat_the_practice_desc;

  /// No description provided for @record_and_compare.
  ///
  /// In en, this message translates to:
  /// **'Record and Compare'**
  String get record_and_compare;

  /// No description provided for @record_and_compare_desc.
  ///
  /// In en, this message translates to:
  /// **'Record yourself while practicing and compare your recording with the audio model to identify differences.'**
  String get record_and_compare_desc;

  /// No description provided for @evaluate_yourself.
  ///
  /// In en, this message translates to:
  /// **'Self Evaluation'**
  String get evaluate_yourself;

  /// No description provided for @evaluate_yourself_desc.
  ///
  /// In en, this message translates to:
  /// **'Review your recording, reflect on your pronunciation, fluency, intonation, and pace to identify strengths and areas for improvement.'**
  String get evaluate_yourself_desc;

  /// No description provided for @continuous_improvement.
  ///
  /// In en, this message translates to:
  /// **'Continuous Improvement'**
  String get continuous_improvement;

  /// No description provided for @continuous_improvement_desc.
  ///
  /// In en, this message translates to:
  /// **'Practice and evaluate your performance consistently to improve your speaking skills.'**
  String get continuous_improvement_desc;

  /// No description provided for @profile_settings.
  ///
  /// In en, this message translates to:
  /// **'Profile & Settings'**
  String get profile_settings;

  /// No description provided for @edit_profile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get edit_profile;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @sound.
  ///
  /// In en, this message translates to:
  /// **'Sound'**
  String get sound;

  /// No description provided for @dark_theme.
  ///
  /// In en, this message translates to:
  /// **'Dark Theme'**
  String get dark_theme;

  /// No description provided for @membership.
  ///
  /// In en, this message translates to:
  /// **'Membership'**
  String get membership;

  /// No description provided for @transaction_history.
  ///
  /// In en, this message translates to:
  /// **'Transaction History'**
  String get transaction_history;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @do_you_understand.
  ///
  /// In en, this message translates to:
  /// **'Do you understand?'**
  String get do_you_understand;

  /// No description provided for @learning_progress.
  ///
  /// In en, this message translates to:
  /// **'Learning Progress'**
  String get learning_progress;

  /// No description provided for @score_acquisition.
  ///
  /// In en, this message translates to:
  /// **'Score Acquisition'**
  String get score_acquisition;

  /// No description provided for @excellent.
  ///
  /// In en, this message translates to:
  /// **'Excellent!🏆'**
  String get excellent;

  /// No description provided for @very_good.
  ///
  /// In en, this message translates to:
  /// **'Very good!👍'**
  String get very_good;

  /// No description provided for @good.
  ///
  /// In en, this message translates to:
  /// **'Good'**
  String get good;

  /// No description provided for @be_better.
  ///
  /// In en, this message translates to:
  /// **'Can be better!💪'**
  String get be_better;

  /// No description provided for @fair.
  ///
  /// In en, this message translates to:
  /// **'Fair enough!🙂'**
  String get fair;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @how_to_answer.
  ///
  /// In en, this message translates to:
  /// **'Instruction'**
  String get how_to_answer;

  /// No description provided for @cv_instruction_decs1.
  ///
  /// In en, this message translates to:
  /// **'Click on the grey text to play the video.'**
  String get cv_instruction_decs1;

  /// No description provided for @cv_instruction_decs2.
  ///
  /// In en, this message translates to:
  /// **'to play the video.'**
  String get cv_instruction_decs2;

  /// No description provided for @cv_instruction_decs3.
  ///
  /// In en, this message translates to:
  /// **'to stop the video.'**
  String get cv_instruction_decs3;

  /// No description provided for @cv_instruction_decs4.
  ///
  /// In en, this message translates to:
  /// **'You can also play the part of the video you want to hear again.'**
  String get cv_instruction_decs4;

  /// No description provided for @cv_instruction_decs5.
  ///
  /// In en, this message translates to:
  /// **'to change the video display ratio to full.'**
  String get cv_instruction_decs5;

  /// No description provided for @cv_instruction_decs6.
  ///
  /// In en, this message translates to:
  /// **'to exit full-ratio video view.'**
  String get cv_instruction_decs6;

  /// No description provided for @cv_result.
  ///
  /// In en, this message translates to:
  /// **'Yay! You finally completed this video conversation section! 🎉'**
  String get cv_result;

  /// No description provided for @click_the_button.
  ///
  /// In en, this message translates to:
  /// **'Click the button'**
  String get click_the_button;

  /// No description provided for @pc_instruction_decs1.
  ///
  /// In en, this message translates to:
  /// **'to listen to the recording.'**
  String get pc_instruction_decs1;

  /// No description provided for @pc_instruction_decs2.
  ///
  /// In en, this message translates to:
  /// **'to send recorded voice.'**
  String get pc_instruction_decs2;

  /// No description provided for @pc_instruction_decs3a.
  ///
  /// In en, this message translates to:
  /// **'Button color changes'**
  String get pc_instruction_decs3a;

  /// No description provided for @pc_instruction_decs3b.
  ///
  /// In en, this message translates to:
  /// **'to record your voice, then click back to send.'**
  String get pc_instruction_decs3b;

  /// No description provided for @pc_instruction_decs4a.
  ///
  /// In en, this message translates to:
  /// **'Not holding the button'**
  String get pc_instruction_decs4a;

  /// No description provided for @pc_instruction_decs4b.
  ///
  /// In en, this message translates to:
  /// **'in order to process the analysis of your voice recording.'**
  String get pc_instruction_decs4b;

  /// No description provided for @pc_instruction_decs5a.
  ///
  /// In en, this message translates to:
  /// **'Don\'t forget to click the button'**
  String get pc_instruction_decs5a;

  /// No description provided for @pc_instruction_decs5b.
  ///
  /// In en, this message translates to:
  /// **'in order to complete a series of challenges.'**
  String get pc_instruction_decs5b;

  /// No description provided for @pc_instruction_decs6a.
  ///
  /// In en, this message translates to:
  /// **'You can also click the button'**
  String get pc_instruction_decs6a;

  /// No description provided for @pc_instruction_decs6b.
  ///
  /// In en, this message translates to:
  /// **'if you are still hesitant in answering the challenge.'**
  String get pc_instruction_decs6b;

  /// No description provided for @lm_instruction_decs1.
  ///
  /// In en, this message translates to:
  /// **'to play audio.'**
  String get lm_instruction_decs1;

  /// No description provided for @lm_instruction_decs2.
  ///
  /// In en, this message translates to:
  /// **'to stop the audio.'**
  String get lm_instruction_decs2;

  /// No description provided for @lm_instruction_decs3.
  ///
  /// In en, this message translates to:
  /// **'You can also replay the part of the audio you want to repeat.'**
  String get lm_instruction_decs3;

  /// No description provided for @lm_instruction_decs4a.
  ///
  /// In en, this message translates to:
  /// **'Don\'t forget to click the button'**
  String get lm_instruction_decs4a;

  /// No description provided for @lm_instruction_decs4b.
  ///
  /// In en, this message translates to:
  /// **'in order to complete a series of challenges.'**
  String get lm_instruction_decs4b;

  /// No description provided for @lm_instruction_decs5a.
  ///
  /// In en, this message translates to:
  /// **'You can also click the button'**
  String get lm_instruction_decs5a;

  /// No description provided for @lm_instruction_decs5b.
  ///
  /// In en, this message translates to:
  /// **'if you are still hesitant in answering the challenge.'**
  String get lm_instruction_decs5b;

  /// No description provided for @record_your_voice.
  ///
  /// In en, this message translates to:
  /// **'Rekam suara anda'**
  String get record_your_voice;

  /// No description provided for @stage.
  ///
  /// In en, this message translates to:
  /// **'Stage'**
  String get stage;

  /// No description provided for @is_logout_desc.
  ///
  /// In en, this message translates to:
  /// **'Do you want to exit this application?'**
  String get is_logout_desc;

  /// No description provided for @repeat.
  ///
  /// In en, this message translates to:
  /// **'Repeat'**
  String get repeat;

  /// No description provided for @evaluation_results.
  ///
  /// In en, this message translates to:
  /// **'Evaluation Results'**
  String get evaluation_results;

  /// No description provided for @score_details.
  ///
  /// In en, this message translates to:
  /// **'Score Details'**
  String get score_details;

  /// No description provided for @impressive_work.
  ///
  /// In en, this message translates to:
  /// **'Impressive work!🌟'**
  String get impressive_work;

  /// No description provided for @bravo.
  ///
  /// In en, this message translates to:
  /// **'Bravo!👏'**
  String get bravo;

  /// No description provided for @getting_closer.
  ///
  /// In en, this message translates to:
  /// **'Getting closer!🔜'**
  String get getting_closer;

  /// No description provided for @tackling_a_tough_one.
  ///
  /// In en, this message translates to:
  /// **'Tackling a tough one!💪'**
  String get tackling_a_tough_one;

  /// No description provided for @interesting_attempt.
  ///
  /// In en, this message translates to:
  /// **'Interesting attempt!😄'**
  String get interesting_attempt;

  /// No description provided for @not_quite_there_yet.
  ///
  /// In en, this message translates to:
  /// **'Not quite there yet!🤔'**
  String get not_quite_there_yet;

  /// No description provided for @keep_practicing.
  ///
  /// In en, this message translates to:
  /// **'Keep practicing!🔄'**
  String get keep_practicing;

  /// No description provided for @great_job.
  ///
  /// In en, this message translates to:
  /// **'Great job!'**
  String get great_job;

  /// No description provided for @good_effort.
  ///
  /// In en, this message translates to:
  /// **'Good effort!'**
  String get good_effort;

  /// No description provided for @needs_improvement.
  ///
  /// In en, this message translates to:
  /// **'Needs improvement!'**
  String get needs_improvement;

  /// No description provided for @accuracy.
  ///
  /// In en, this message translates to:
  /// **'Accuracy'**
  String get accuracy;

  /// No description provided for @your_score.
  ///
  /// In en, this message translates to:
  /// **'Your score'**
  String get your_score;

  /// No description provided for @vocabulary.
  ///
  /// In en, this message translates to:
  /// **'Vocabulary'**
  String get vocabulary;

  /// No description provided for @part.
  ///
  /// In en, this message translates to:
  /// **'Part'**
  String get part;

  /// No description provided for @exercise.
  ///
  /// In en, this message translates to:
  /// **'Exercise'**
  String get exercise;

  /// No description provided for @your_answer_is_correct.
  ///
  /// In en, this message translates to:
  /// **'Your answer is correct! 🤩🤗'**
  String get your_answer_is_correct;

  /// No description provided for @your_answer_is_wrong.
  ///
  /// In en, this message translates to:
  /// **'Your answer is wrong! 😫😭'**
  String get your_answer_is_wrong;

  /// No description provided for @correct.
  ///
  /// In en, this message translates to:
  /// **'Correct'**
  String get correct;

  /// No description provided for @wrong.
  ///
  /// In en, this message translates to:
  /// **'Incorrect'**
  String get wrong;

  /// No description provided for @continueYourLessons.
  ///
  /// In en, this message translates to:
  /// **'Keep learning, crush your goals!📚'**
  String get continueYourLessons;

  /// No description provided for @learning_material.
  ///
  /// In en, this message translates to:
  /// **'Finish it, can\'t wait to see the result! 🤩'**
  String get learning_material;

  /// No description provided for @explore_your_potential.
  ///
  /// In en, this message translates to:
  /// **'Explore your potential! 🌍📱📚'**
  String get explore_your_potential;

  /// No description provided for @unlock_opportunities.
  ///
  /// In en, this message translates to:
  /// **'Ready to unlock a world of opportunities?'**
  String get unlock_opportunities;

  /// No description provided for @start_journey.
  ///
  /// In en, this message translates to:
  /// **'It\'s time to embark on a learning journey'**
  String get start_journey;

  /// No description provided for @get_started.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get get_started;

  /// No description provided for @prosody.
  ///
  /// In en, this message translates to:
  /// **'Intonation & Rythm'**
  String get prosody;

  /// No description provided for @completeness.
  ///
  /// In en, this message translates to:
  /// **'Completeness'**
  String get completeness;

  /// No description provided for @unit.
  ///
  /// In en, this message translates to:
  /// **'Unit'**
  String get unit;

  /// No description provided for @chapter_list.
  ///
  /// In en, this message translates to:
  /// **'Chapter List'**
  String get chapter_list;

  /// No description provided for @more.
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get more;

  /// No description provided for @listening_exercise.
  ///
  /// In en, this message translates to:
  /// **'Listening Exercise'**
  String get listening_exercise;

  /// No description provided for @skills_list.
  ///
  /// In en, this message translates to:
  /// **'Skills List'**
  String get skills_list;

  /// No description provided for @explore_the_courses.
  ///
  /// In en, this message translates to:
  /// **'Explore the courses'**
  String get explore_the_courses;

  /// No description provided for @level_pitch_sentences.
  ///
  /// In en, this message translates to:
  /// **'From basic communication to full mastery, these levels guide you on your way to English fluency.'**
  String get level_pitch_sentences;

  /// No description provided for @remember_7_items.
  ///
  /// In en, this message translates to:
  /// **'Remember 7 items'**
  String get remember_7_items;

  /// No description provided for @tap_items_you_saw.
  ///
  /// In en, this message translates to:
  /// **'Tap the items you saw'**
  String get tap_items_you_saw;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading'**
  String get loading;

  /// No description provided for @ready.
  ///
  /// In en, this message translates to:
  /// **'Siap?'**
  String get ready;

  /// No description provided for @go.
  ///
  /// In en, this message translates to:
  /// **'Mulai!'**
  String get go;

  /// No description provided for @memory_flash_result_desc.
  ///
  /// In en, this message translates to:
  /// **'You’ve done it! Let’s check your result 📋'**
  String get memory_flash_result_desc;

  /// No description provided for @replay.
  ///
  /// In en, this message translates to:
  /// **'Replay'**
  String get replay;

  /// No description provided for @topic_complete.
  ///
  /// In en, this message translates to:
  /// **'Topic Complete 📑'**
  String get topic_complete;

  /// No description provided for @level_complete.
  ///
  /// In en, this message translates to:
  /// **'Level Complete🏅 '**
  String get level_complete;

  /// No description provided for @congratulations.
  ///
  /// In en, this message translates to:
  /// **'Congratulations! 🎉'**
  String get congratulations;

  /// No description provided for @score.
  ///
  /// In en, this message translates to:
  /// **'Score'**
  String get score;

  /// No description provided for @select_category.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get select_category;

  /// No description provided for @select_topic.
  ///
  /// In en, this message translates to:
  /// **'Select Topic'**
  String get select_topic;

  /// No description provided for @please_wait.
  ///
  /// In en, this message translates to:
  /// **'Please wait'**
  String get please_wait;

  /// No description provided for @complete_all_challenges.
  ///
  /// In en, this message translates to:
  /// **'Complete All Challenges'**
  String get complete_all_challenges;

  /// No description provided for @complete_all_challenges_desc.
  ///
  /// In en, this message translates to:
  /// **'Please complete all challenges to view your final results.'**
  String get complete_all_challenges_desc;

  /// No description provided for @gameplay.
  ///
  /// In en, this message translates to:
  /// **'Gameplay 🕹️'**
  String get gameplay;

  /// No description provided for @how_to_play_memory_flash.
  ///
  /// In en, this message translates to:
  /// **'See 7 words for 3 seconds each, then pick them from a list to 10 ( 7 correct + 3 distractors).'**
  String get how_to_play_memory_flash;

  /// No description provided for @cert_notif_a1.
  ///
  /// In en, this message translates to:
  /// **'👏 Congratulations!\nYou have completed Level A1'**
  String get cert_notif_a1;

  /// No description provided for @cert_notif_a2.
  ///
  /// In en, this message translates to:
  /// **'🌟 Your English Skills Are Growing Fast!'**
  String get cert_notif_a2;

  /// No description provided for @cert_notif_b1.
  ///
  /// In en, this message translates to:
  /// **'💪 Reaching B1 Means You Can Communicate with Ease!'**
  String get cert_notif_b1;

  /// No description provided for @cert_notif_b2.
  ///
  /// In en, this message translates to:
  /// **'🚀 Completing B2 Shows Your Dedication!'**
  String get cert_notif_b2;

  /// No description provided for @cert_notif_c1.
  ///
  /// In en, this message translates to:
  /// **'🔥 You’ve Reached Advanced Mastery!'**
  String get cert_notif_c1;

  /// No description provided for @cert_notif_c2.
  ///
  /// In en, this message translates to:
  /// **'👑 You’ve Reached the Top!'**
  String get cert_notif_c2;

  /// No description provided for @level_not_completed.
  ///
  /// In en, this message translates to:
  /// **'Level Not Completed'**
  String get level_not_completed;

  /// No description provided for @level_not_completed_desc.
  ///
  /// In en, this message translates to:
  /// **'Please complete all chapters and sections in this level to unlock your certificate.'**
  String get level_not_completed_desc;

  /// No description provided for @back_to_lessons.
  ///
  /// In en, this message translates to:
  /// **'Back to Lessons'**
  String get back_to_lessons;

  /// No description provided for @cert_download_a1.
  ///
  /// In en, this message translates to:
  /// **'🎓 Download your A1 Foundation Certificate and showcase your English basics!'**
  String get cert_download_a1;

  /// No description provided for @cert_download_a2.
  ///
  /// In en, this message translates to:
  /// **'📜 Get your A2 Elementary Certificate - proof of your growing English skills!'**
  String get cert_download_a2;

  /// No description provided for @cert_download_b1.
  ///
  /// In en, this message translates to:
  /// **'🏆 Download your B1 Intermediate Certificate and celebrate your communication milestone!'**
  String get cert_download_b1;

  /// No description provided for @cert_download_b2.
  ///
  /// In en, this message translates to:
  /// **'⭐ Claim your B2 Upper-Intermediate Certificate - you\'re almost at advanced level!'**
  String get cert_download_b2;

  /// No description provided for @cert_download_c1.
  ///
  /// In en, this message translates to:
  /// **'🥇 Download your C1 Advanced Certificate - a testament to your English mastery!'**
  String get cert_download_c1;

  /// No description provided for @cert_download_c2.
  ///
  /// In en, this message translates to:
  /// **'👑 Get your C2 Proficiency Certificate - the ultimate proof of English excellence!'**
  String get cert_download_c2;

  /// No description provided for @certificate_list.
  ///
  /// In en, this message translates to:
  /// **'Certificate List'**
  String get certificate_list;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'id'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'id':
      return AppLocalizationsId();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
